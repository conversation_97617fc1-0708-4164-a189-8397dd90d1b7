/**
 * @file        tle5012-conf-opts.hpp
 * @brief       TLE5012 Library Configuration Options
 * @date        Oct 2020
 * @copyright   Copyright (c) 2019-2020 Infineon Technologies AG
 *
 * SPDX-License-Identifier: MIT
 */

/**
 * @addtogroup tle5012frmw
 * @{
 */

#ifndef TLE5012_CONF_OPTS_HPP_
#define TLE5012_CONF_OPTS_HPP_

//!< \brief List of available platforms
#define TLE5012_FRMWK_ARDUINO   0x01U
#define TLE5012_FRMWK_WICED     0x02U
#define TLE5012_FRMWK_MTB       0x03U
#define TLE5012_FRMWK_PSOC      0x04U

/** @} */

#endif /** TLE5012_CONF_OPTS_HPP_ **/