name: PlatformIO CI

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest
    #os: [ubuntu-latest, macos-latest, windows-latest]
    strategy:
      matrix:
        example: [
            examples/E9000SPC,
            examples/readAngleSpeedRevolutions,
            examples/readAngleTest,
            examples/readAngleValueProcessing,
            examples/readMultipleRegisters,
            examples/readSpeedProcessing,
            examples/sensorRegisters,
            examples/sensorType,
            examples/testSensorMainValues,
            examples/useMultipleSensors,
            examples/writeRegisters
        ]

    steps:

    - uses: actions/checkout@v2
    - name: Cache pip
      uses: actions/cache@v2
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: ${{ runner.os }}-pip-

    - name: Cache PlatformIO
      uses: actions/cache@v2
      with:
        path: ~/.platformio
        key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}

    - name: Set up Python
      uses: actions/setup-python@v2

    - name: Install PlatformIO
      run: |
        python -m pip install --upgrade pip
        pip install --upgrade platformio
        platformio platform install -f infineonxmc
        platformio platform install -f atmelavr

    - name: Run PlatformIO
      run: |
        pio ci --lib="." --board=xmc1100_xmc2go --board=xmc1100_boot_kit --board=xmc4700_relax_kit --board=uno --project-option="lib_deps=Wire"
      env:
        PLATFORMIO_CI_SRC: ${{ matrix.example }}
