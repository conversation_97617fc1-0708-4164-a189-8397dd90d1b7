//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/SPI/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Wire/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/NN/Include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/DSP/Include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/Include",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/LIBS",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/XMCLib/inc",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Class",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Class/Device",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Common",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Core",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Core/XMC4000",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/avr",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/variants/XMC4700/config/XMC4700_Relax_Kit",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/variants/XMC4700",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.git",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.github",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.pio",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.vscode",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/docs",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/examples",
                "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/processing",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/BGT24LTR11-Pulsed-Doppler-Radar/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DMA/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DSP/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DeviceControlXMC/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/I2S/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/IFX9201/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/LED/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Mouse/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/OneWire/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/RTC/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Radar/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/USB/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Ultrasonic/src",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/SPI/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Wire/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/NN/Include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/DSP/Include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/CMSIS/Include",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/LIBS",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/xmc_lib/XMCLib/inc",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Class",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Class/Device",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Common",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Core",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/usblib/Core/XMC4000",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/cores/avr",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/variants/XMC4700/config/XMC4700_Relax_Kit",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/variants/XMC4700",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.git",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.github",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.pio",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.vscode",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/docs",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/examples",
                    "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/processing",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/BGT24LTR11-Pulsed-Doppler-Radar/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DMA/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DSP/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/DeviceControlXMC/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/I2S/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/IFX9201/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/LED/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Mouse/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/OneWire/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/RTC/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Radar/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/USB/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduinoxmc/libraries/Ultrasonic/src",
                    ""
                ]
            },
            "defines": [
                "F_CPU=144000000L",
                "XMC4700",
                "ARM_MATH_DSP",
                "ARM_MATH_CM4",
                "USB0",
                "_INIT_DECLARATION_REQUIRED",
                "PLATFORMIO=60118",
                "XMC4700_Relax_Kit",
                "ARDUINO=4700",
                "XMC4700_F144x2048",
                "ARDUINO_ARCH_ARM",
                "ARDUINO=10805",
                ""
            ],
            "cStandard": "gnu11",
            "cppStandard": "gnu++11",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin/arm-none-eabi-gcc.exe",
            "compilerArgs": [
                "-mthumb",
                "-mcpu=cortex-m4",
                ""
            ]
        }
    ],
    "version": 4
}
