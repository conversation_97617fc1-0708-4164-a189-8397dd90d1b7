## How to contribute to Infineon examples, libraries and code

 

#### **Did you find a bug?**

 

* **verify that the library belongs to you hardware**, sometimes certain sensors look similar but have different functionality and

different software repositories. Check if you have the right combination.

 

* **Ensure the bug was not already reported** by searching on GitHub under issues for this particular repository

 

* If you're unable to find an open issue addressing the problem, open a new one. Be sure to include

    - a **title and clear description**, as much relevant information as possible

    - a **code sample** or an **executable test case** demonstrating the expected behavior that is not occurring

    - a **hardware list** the exact name of a sensors or mikrocontrollers you used

    - a **circuit diagram** if you have a such a problem

 

#### **Did you write a patch that fixes a bug?**

 

* Before you write a patch open an issue and wait for answer an answer from the repository admin. Maybe a patch or a new release is already and the way.

 

* Open a new GitHub pull request with the patch.

 

* Ensure the PR description clearly describes the problem and solution. Include the relevant issue number if applicable.

 

#### **Did you fix whitespace, format code, or make a purely cosmetic patch?**

 

Changes that are cosmetic in nature and do not add anything substantial to the stability, functionality, or testability of Rails will generally not be accepted

as a patch, but you can still raise an issue so that these changes can be fixed with a new official release.

 

#### **Do you intend to add a new feature or change an existing one?**

 

* Suggest your change in by raising an issue before you start writing code.

 

* Do not open an issue on GitHub until you have collected positive feedback about the change.

 

* Start a discussion with the [Infineon Maker Team](https://github.com/orgs/Infineon/teams/maker-hackathon-team)

 

#### **Do you have questions about the source code?**

 

* Please start a discussion with the [Infineon Maker Team](https://github.com/orgs/Infineon/teams/maker-hackathon-team)

 

#### **Do you want to contribute to the documentation?**

 

* Same, start a discussion with the [Infineon Maker Team](https://github.com/orgs/Infineon/teams/maker-hackathon-team)

 

 

Thanks! :heart: :heart: :heart:

 

Infineon Technologies AG, GitHub Team