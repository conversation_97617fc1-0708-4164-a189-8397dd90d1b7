{"build_type": "release", "env_name": "xmc4700_relax_kit", "libsource_dirs": ["C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.pio\\libdeps\\xmc4700_relax_kit", "C:\\Users\\<USER>\\.platformio\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries"], "defines": ["F_CPU=144000000L", "XMC4700", "ARM_MATH_DSP", "ARM_MATH_CM4", "USB0", "_INIT_DECLARATION_REQUIRED", "PLATFORMIO=60118", "XMC4700_Relax_Kit", "ARDUINO=4700", "XMC4700_F144x2048", "ARDUINO_ARCH_ARM", "ARDUINO=10805"], "includes": {"build": ["C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\Wire\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\xmc_lib\\CMSIS\\NN\\Include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\xmc_lib\\CMSIS\\DSP\\Include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\xmc_lib\\CMSIS\\Include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\xmc_lib\\LIBS", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\xmc_lib\\XMCLib\\inc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib\\Class", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib\\Class\\Device", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib\\Common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib\\Core", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\usblib\\Core\\XMC4000", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\cores\\avr", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\variants\\XMC4700\\config\\XMC4700_Relax_Kit", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\variants\\XMC4700"], "compatlib": ["C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\Wire\\src", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.git", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.github", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.pio", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.vscode", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\docs", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\examples", "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\processing", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\BGT24LTR11-Pulsed-Doppler-Radar\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\DMA\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\DSP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\DeviceControlXMC\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\I2S\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\IFX9201\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\LED\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\Mouse\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\OneWire\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\RTC\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\Radar\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\USB\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoxmc\\libraries\\Ultrasonic\\src"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\arm-none-eabi\\include\\c++\\7.2.1", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\arm-none-eabi\\include\\c++\\7.2.1\\arm-none-eabi", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\lib\\gcc\\arm-none-eabi\\7.2.1\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\lib\\gcc\\arm-none-eabi\\7.2.1\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\arm-none-eabi\\include"]}, "cc_flags": ["-M<PERSON>", "-std=gnu11", "-<PERSON><PERSON>", "-w", "-nostdlib", "-Wall", "-ffunction-sections", "-fdata-sections", "-mthumb", "-mcpu=cortex-m4"], "cxx_flags": ["-fno-exceptions", "-fno-threadsafe-statics", "-fpermissive", "-mthumb", "-std=gnu++11", "-<PERSON><PERSON>", "-w", "-nostdlib", "-Wall", "-ffunction-sections", "-fdata-sections", "-mthumb", "-mcpu=cortex-m4"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\bin\\arm-none-eabi-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\bin\\arm-none-eabi-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi\\bin\\arm-none-eabi-gdb.exe", "prog_path": "C:\\Users\\<USER>\\Desktop\\Code\\TLE5012-Magnetic-Angle-Sensor\\.pio\\build\\xmc4700_relax_kit\\program.elf", "svd_path": "C:\\Users\\<USER>\\.platformio\\platforms\\infineonxmc\\misc\\svd\\XMC4700.svd", "compiler_type": "gcc", "targets": [{"name": "upload", "group": "Platform", "title": "Upload"}], "extra": {"flash_images": []}}