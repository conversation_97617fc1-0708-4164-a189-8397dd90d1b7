
/*!
 * \name        const
 * \author      Infineon Technologies AG (Dr.<PERSON>)
 * \copyright   2020 Infineon Technologies AG
 * \version     3.1.0
 * \brief       macros and other const variables
 *
 * \attention
 * Ths setup reduces variable memory so that the sensorRegisters can run on MCUs
 * with low memory by shifting global variable memory to main programm memory.
 */


#define sc_PCB            F("Identified Sensor PCB: ")
#define sc_Interface      F("Identified Interface:  ")
#define sc_slave          F("Identified Slave:      ")
#define sc_slavename      F(" = TLE5012_S")
#define sc_enable         F("enabled")
#define sc_disable        F("disabled")
#define sc_PushPull       F("Push-Pull")
#define sc_OpenDrain      F("Open Drain")
#define sc_NotAllowed     F("not allowed")
  
// PWM interface  
#define sc_E3005          F(" = TLE5012B_E3005")
#define sc_E5000          F(" = TLE5012B_E5000")
#define sc_PWM            F(" = PWM")
#define sc_ifsetPWM       F("\nPWM Interface settings\n")
#define sc_PWMfreq        F("\nMOD4 Frequnecy:         \t")
#define sc_PWMtime        F("\nIFAB Unit time:         \t")
#define sc_PWMfilter      F("\nIFAB Filter update time:\t")
#define sc_PWMpin         F("\nIFAB IFA pin:           \t")
  
//IIF Interface  
#define sc_E1000          F(" = TLE5012B_E1000")
#define sc_IIF            F(" = IIF")
#define sc_ifsetIIF       F("\nIIF Interface settings\n")
#define sc_IIFmod         F("\nMOD1 IIF Mode:          \t")
#define sc_IIFres         F("\nMOD4 Resolution:        \t")
#define sc_IIFhyster      F("\nIFAB Hysteresis:        \t")
#define sc_IIFfilter      F("\nIFAB Filter update time:\t")
#define sc_IIFpin         F("\nIFAB IFA pin:           \t")
#define sc_IIFab          F("A/B operation with Index on IFC pin")
#define sc_IIFdirection   F("Step/Direction operation with Index on IFC pin")
  
// HSM Interface  
#define sc_E5020          F(" = TLE5012B_E5020")
#define sc_HSM            F(" = HSM")
#define sc_ifsetHSM       F("\nHSM Interface settings\n")
#define sc_HSMframe       F("\nMOD4 Frame config:      \t")
#define sc_HSMhyster      F("\nIFAB Hysteresis:        \t")
#define sc_HSMfilter      F("\nIFAB Filter update time:\t")
#define sc_HSMpin         F("\nIFAB IFA pin:           \t")
  
// SPC Interface  
#define sc_E9000          F(" = TLE5012B_E9000")
#define sc_SPC            F(" = SPC")
#define sc_ifsetSPC       F("\nSPC Interface settings\n")
#define sc_SPCframe       F("\nMOD4 Frame config:      \t")
#define sc_SPCtime        F("\nIFAB Unit time:         \t")
#define sc_SPCpin         F("\nIFAB IFA pin:           \t")

// additional
#define sc_MOD2cal        F("\nMOD2 Autocalibration:   \t")
#define sc_MOD2predict    F("\nMOD2 Prediction:        \t")
#define sc_MODpin         F("\nMOD3 IFA/B/C pin set:   \t")
#define sc_MODstrongfast  F("strong driver, DATA: strong driver, fast edge")
#define sc_MODstrongslow  F("strong driver, DATA: strong driver, slow edge")
#define sc_MODweakfast    F("weak driver, DATA: medium driver, fast edge")
#define sc_MODweakslow    F("weak driver, DATA: weak driver, slow edge")
#define sc_MODssc         F("\nMOD3 SSC interface:     \t")
#define sc_MODvoltage     F("\nMOD3 Voltage filter:    \t")
#define sc_Spikeenable    F("spike filter enabled")
#define sc_Spikedisbale   F("spike filter disabled")
#define sc_Angle12        F("12bit angle")
#define sc_Angle16        F("16bit angle")
#define sc_Angle12Temp    F("12bit angle + 8bit temperature")
#define sc_Angle16Temp    F("16bit angle + 8bit temperature")
#define sc_Nocalib        F("no auto-calibration")
#define sc_Cal1           F("auto-cal. mode 1: update every angle update cycle")
#define sc_Cal2           F("auto-cal. mode 2: update every 1.5 revolutions")
#define sc_Cal3           F("auto-cal. mode 3: update every 11.25°")

// registers
#define sc_T250           F("Signed offset value at 25°C temperature; 1dig=0.36°C.")
#define sc_TRAWIIFCNT     F("Internal 14-bit counter for the incremental interface, counts from 0 to 16383 during one full turn")
#define sc_TRAWTTGL       F("Toggles after every new temperature value")
#define sc_MAG            F("Unsigned Angle Vector Magnitude after X, Y error")
#define sc_TCOYT          F("7-bit signed integer value of Y-offset temperature coefficient")
#define sc_TCOXT          F("7-bit signed integer value of X-offset temperature coefficient")
#define sc_CPAR           F("CRC of parameters from address 08H to 0FH")
#define sc_BISTenable     F("Startup-BIST enabled")
#define sc_BISTdisable    F("Startup-BIST disabled")
#define sc_IFDM           F("Interface Mode on IFA,IFB,IFC: ")
#define sc_SYNCH          F("12-bit signed integer value of amplitude synchronicity correction")
#define sc_YOFFSET        F("12-bit signed integer value of raw Y-signal offset")
#define sc_XOFFSET        F("12-bit signed integer value of raw X-signal offset")

// REG_STAT
#define sc_srst           F("SRST      ")
#define sc_swd            F("SWD       ")
#define sc_svr            F("SVR       ")
#define sc_sfuse          F("SFUSE     ")
#define sc_sdspu          F("SDSPU     ")
#define sc_sov            F("SOV       ")
#define sc_sxyol          F("SXYOL     ")
#define sc_smagol         F("SMAGOL    ")
#define sc_sadct          F("SADCT     ")
#define sc_srom           F("SROM      ")
#define sc_nogmrxy        F("NOGMRXY   ")
#define sc_nogmra         F("NOGMRA    ")
#define sc_rdst           F("RDST      ")
#define sc_snr            F("SNR       ")
#define sc_SRST0          F("indication of power-up, short power-break, firmware or active reset")
#define sc_SRST1          F("no reset since last readout")
#define sc_SWD0           F("watch dog counter expired (DSPU stop)")
#define sc_SWD1           F("normal operation")
#define sc_SVR0           F("over voltage VDD-off, GND-off or VOVG/A/D too high")
#define sc_SVR1           F("Voltages Ok")
#define sc_SFUSE0         F("CRC on fuse fail")
#define sc_SFUSE1         F("CRC on fuse Ok")
#define sc_SDSPU0         F("DPSU self-test not Ok or still running")
#define sc_SDSPU1         F("DPSU self-test Ok")
#define sc_SOV0           F("DSPU overflow occurred")
#define sc_SOV1           F("No DSPU overflow occurred")
#define sc_SXYOL0         F("X/Y data out of limit")
#define sc_SXYOL1         F("X/Y data Ok")
#define sc_SMAGOL0        F("GMR magnitude out of limit")
#define sc_SMAGOL1        F("GMR magnitude Ok")
#define sc_SADCT0         F("Test vectors out of limit")
#define sc_SADCT1         F("Test vectors Ok")
#define sc_SROM0          F("CRC fail or still running")
#define sc_SROM1          F("CRC Ok")
#define sc_NOGMRXY0       F("no valid GMR_XY values on the ADC input")
#define sc_NOGMRXY1       F("valid GMR_XY values on the ADC input and thus on filter output")
#define sc_NOGMRA0        F("no valid GMR angle value on the interface")
#define sc_NOGMRA1        F("valid GMR angle value on the interface")
#define sc_SNR0           F("Slave Number of this sensor out of up to four sensors")
#define sc_RDST0          F("status values changed")
#define sc_RDST1          F("status values not changed since last readout")

// REG_ACSTAT
#define sc_asrst          F("ASRST     ")
#define sc_aswd           F("ASWD      ")
#define sc_asvr           F("ASVR      ")
#define sc_asfuse         F("ASFUSE    ")
#define sc_asdspu         F("ASDSPU    ")
#define sc_asov           F("ASOV      ")
#define sc_asvecxy        F("ASVECXY   ")
#define sc_asvegmag       F("ASVEGMAG  ")
#define sc_asadct         F("ASADCT    ")
#define sc_asfrst         F("ASFRST    ")
#define sc_ASRST0         F("activation of HW Reset (S_RST is set)")
#define sc_ASRST1         F("after execution (write only, thus always returns 0)")
#define sc_ASWD0          F("DSPU Watch dog monitoring enabled")
#define sc_ASWD1          F("DSPU watch dog monitoring disabled")
#define sc_ASVR0          F("check of regulator voltages enabled")
#define sc_ASVR1          F("check of regulator voltages disabled")
#define sc_ASFUSE0        F("monitoring of CRC enabled")
#define sc_ASFUSE1        F("monitoring of CRC disabled")
#define sc_ASDSPU0        F("activation of DSPU BIST or BIST running")
#define sc_ASDSPU1        F("after execution")
#define sc_ASOV0          F("monitoring of DSPU Overflow enabled")
#define sc_ASOV1          F("monitoring of DSPU Overflow disabled")
#define sc_ASVECXY0       F("monitoring of X,Y Out of Limit enabled")
#define sc_ASVECXY1       F("monitoring of X,Y Out of Limit disabled")
#define sc_ASVEGMAG0      F("monitoring of magnitude enabled")
#define sc_ASVEGMAG1      F("monitoring of magnitude disabled")
#define sc_ASADCT0        F("after execution")
#define sc_ASADCT1        F("activation of ADC Test vector Check")
#define sc_ASFRST0        F("default or after execution of firmware reset")
#define sc_ASFRST1        F("activation of firmware reset")

// REG_AVAL
#define sc_rdav           F("RDAV      ")
#define sc_angval         F("ANGVAL    ")
#define sc_RDAV0          F("new angle value present")
#define sc_RDAV1          F("no new angle value since last readout")
#define sc_ANGVAL         F("Calculated Angle Value (signed 15-bit)")

// REG_ASPD
#define sc_rdas           F("RDAS      ")
#define sc_angspd         F("ANGSPD    ")
#define sc_RDAS0          F("new angle speed value present")
#define sc_RDAS1          F("no new speed angle value since last readout")
#define sc_ANGSPD         F("Calculated Angle Speed, where the sign indicates the direction of the rotation")

// REG_AREV
#define sc_rdrev          F("RDREV     ")
#define sc_fcnt           F("FCNT      ")
#define sc_revol          F("REVOL     ")
#define sc_RDREV0         F("new value present")
#define sc_RDREV1         F("no new value since last readout")
#define sc_FCNT           F("Internal frame counter. Increments every update period (FIR_MD setting)")
#define sc_REVOL          F("Number of Revolutions (signed 9-bit value)")

// REG_FSYNC
#define sc_tempr          F("TEMPR     ")
#define sc_fsync          F("FSYNC     ")
#define sc_TEMPR          F("Signed offset compensated temperature value")
#define sc_FSYNC          F("Frame Synchronization Counter Value")

// REG_MOD_1
#define sc_mod1           F("IIFMOD    ")
#define sc_IIF0           F("Incremental Interface Mode: 00b IIF disabled")
#define sc_IIF1           F("Incremental Interface Mode: 01b A/B operation with Index on IFC pin")
#define sc_IIF2           F("Incremental Interface Mode: 10b Step/Direction operation with Index on IFC pin")
#define sc_IIF3           F("Incremental Interface Mode: 11b not allowed")
#define sc_dsphold        F("DSPUHOLD  ")
#define sc_DSPU0          F("DSPU is on hold")
#define sc_DSPU1          F("DSPU in normal schedule operation")
#define sc_clksel         F("CLKSEL    ")
#define sc_CLKS0          F("external 4-MHz clock (IFC pin switched to input)")
#define sc_CLKS1          F("internal oscillator")
#define sc_firmd          F("FIRMD     ")
#define sc_FIRMD0         F("Update Rate Setting (Filter Decimation): 00b none")
#define sc_FIRMD1         F("Update Rate Setting (Filter Decimation): 01b 42.7 μs")
#define sc_FIRMD2         F("Update Rate Setting (Filter Decimation): 10b 85.3 μs")
#define sc_FIRMD3         F("Update Rate Setting (Filter Decimation): 11b 170.6 μs")

// REG_SIL
#define sc_filterpar      F("FILTPAR   ")
#define sc_FPAR0          F("filter parallel enabled (source: X-value)")
#define sc_FPAR1          F("filter parallel disabled")

#define sc_adctvx         F("ADCTVX    ")
#define sc_adctvy         F("ADCTVY    ")
#define sc_adctven        F("ADCTVEN   ")
#define sc_AVEN0          F("ADC-Test Vectors enabled")
#define sc_AVEN1          F("ADC-Test Vectors disabled")

#define sc_filtinv        F("FILTINV   ")
#define sc_FINV0          F("filter inverted enabled")
#define sc_FINV1          F("filter inverted disabled")
#define sc_fuserel        F("FUSEREL   ")
#define sc_FREL0          F("normal operation")
#define sc_FREL1          F("reload of registers with fuse values immediately")

#define sc_ADCX           F("Test vector X: ")
#define sc_ADCY           F("Test vector Y: ")
#define sc_adctv0         F("000b 0V")
#define sc_adctv1         F("001b +70%")
#define sc_adctv2         F("010b +100%")
#define sc_adctv3         F("011b +Overflow")
#define sc_adctv4         F("101b -70%")
#define sc_adctv5         F("110b -100%")
#define sc_adctv6         F("111b -Overflow")

// REG_MOD_2
#define sc_angrange       F("ANGRANGE  ")
#define sc_arng0          F("080h factor 1 (default), magnetic angle -180°..180°")
#define sc_arng1          F("200h factor 4, magnetic angle -45°..45°")
#define sc_arng2          F("040h factor 0.5, magnetic angle -180°..180°")
#define sc_angdir         F("ANGDIR    ")
#define sc_adir0          F("counterclockwise rotation of magnet")
#define sc_adir1          F("clockwise rotation of magnet")
#define sc_predict        F("PREDICT   ")
#define sc_pred0          F("prediction enabled")
#define sc_pred1          F("prediction disabled")
#define sc_autocal        F("AUTOCAL   ")
#define sc_aucl0          F("00b no auto-calibration")
#define sc_aucl1          F("01b auto-cal. mode 1: update every angle update cycle")
#define sc_aucl2          F("10b auto-cal. mode 2: update every 1.5 revolutions")
#define sc_aucl3          F("11b auto-cal. mode 3: update every 11.25°")

// REG_MOD_2
#define sc_angbase        F("ANG_BASE  ")
#define sc_paddrv         F("PADDRV    ")
#define sc_sscod          F("SSCOD     ")
#define sc_spikef         F("SPIKEF    ")
#define sc_angb0          F("800H -180°")
#define sc_angb1          F("000H 0°")
#define sc_angb2          F("7FFH +179.912°")
#define sc_pad0           F("00b IFA/IFB/IFC: strong driver, DATA: strong driver, fast edge")
#define sc_pad1           F("01b IFA/IFB/IFC: strong driver, DATA: strong driver, slow edge")
#define sc_pad2           F("10B IFA/IFB/IFC: weak driver, DATA: medium driver, fast edge")
#define sc_pad3           F("11B IFA/IFB/IFC: weak driver, DATA: weak driver, slow edge")
#define sc_ssco0          F("Push-Pull")
#define sc_ssco1          F("Open Drain")
#define sc_spik0          F("spike filter enabled")
#define sc_spik1          F("spike filter disabled")

// REG_OFFX REG_OFFY REG_SYNCH
#define sc_xoff           F("XOFFSET   ")
#define sc_yoff           F("YOFFSET   ")
#define sc_sync           F("SYNCH     ")

// REG_IFAB
#define sc_ortho          F("ORTHO     ")
#define sc_hyst           F("IFADHYST  ")
#define sc_ifabod         F("IFABOD    ")
#define sc_fir            F("FIRUDR    ")
#define sc_ORTH           F("12-bit signed integer value of orthogonality correction")
#define sc_fir0           F("FIR_MD = ‘10’ (85.3 μs)")
#define sc_fir1           F("FIR_MD = ‘01’ (42.7 μs)")

// REG_MOD_4
#define sc_ifmd           F("IFMD      ")
#define sc_hsmplp         F("HSMPLP    ")
#define sc_ifabres        F("IFABRES   ")
#define sc_tcoxt          F("TCOXT     ")
#define sc_IFDM0          F("Interface Mode on IFA,IFB,IFC: ")
#define sc_tcoyt          F("TCOYT     ")
#define sc_hsm0           F("IIF Absolute Count, x0xxb enabled, x1xxb disabled")
#define sc_hsm1           F("PWM Error Indication, xx0xb enabled, xx1xb disabled")
#define sc_hsm2           F("HSM Pole-Pair Configuration 0000b=1 - 1111b=16")
#define sc_hsm3           F("SPC Total Trigger Time, 0000b 90*UT, 0100b tmlow + 12 UT")
#define sc_hsm4           F("none")


#define sc_crcpar         F("CRCPAR    ")
#define sc_sbist          F("SBIST     ")
#define sc_adcx           F("ADCX      ")
#define sc_adcy           F("ADCY      ")
#define sc_mag            F("MAG       ")
#define sc_ttgl           F("TTGL      ")
#define sc_iifcnt         F("IIFCNT    ")
#define sc_t250           F("T250      ")
