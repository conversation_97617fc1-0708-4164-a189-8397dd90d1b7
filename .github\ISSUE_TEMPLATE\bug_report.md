---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: <PERSON><PERSON><PERSON><PERSON>, ed<PERSON><PERSON><PERSON>, j<PERSON><PERSON>-if<PERSON>, <PERSON><PERSON><PERSON><PERSON>

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Software (please complete the following information):**
 - OS: [e.g. Windows 10, Linux Ubuntu 18.04]
 - IDE name and version [e.g. Arduino IDE 1.8.10]
 - Libraries with full name and version:[ eg: DC-Motor-Control-TLE94112EL V1.4.1]
 - XMC-for-Arduino with version: [eg: XMC4Arduino 1.2.1]

**Hardware (please complete the following information):**
 - Sensor with full name: [e.g. TLV463D-A1B6-3DMagnetic-Sensor]
 - Shield with full name : [e.g. TLE94112EL]
 - Circuit diagram if needed: [e.g. a Fritzing picture]
 - other Hardware if involved

**Additional context**
Add any other context about the problem here.
