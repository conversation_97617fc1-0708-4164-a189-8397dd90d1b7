// AUTOMATICALLY GENERATED FILE. PLEASE DO NOT MODIFY IT MANUALLY
//
// PlatformIO Debugging Solution
//
// Documentation: https://docs.platformio.org/en/latest/plus/debugging.html
// Configuration: https://docs.platformio.org/en/latest/projectconf/sections/env/options/debug/index.html

{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug",
            "executable": "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.pio/build/xmc4700_relax_kit/program.elf",
            "projectEnvName": "xmc4700_relax_kit",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/infineonxmc/misc/svd/XMC4700.svd",
            "preLaunchTask": {
                "type": "PlatformIO",
                "task": "Pre-Debug"
            }
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (skip Pre-Debug)",
            "executable": "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.pio/build/xmc4700_relax_kit/program.elf",
            "projectEnvName": "xmc4700_relax_kit",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/infineonxmc/misc/svd/XMC4700.svd"
        },
        {
            "type": "platformio-debug",
            "request": "launch",
            "name": "PIO Debug (without uploading)",
            "executable": "C:/Users/<USER>/Desktop/Code/TLE5012-Magnetic-Angle-Sensor/.pio/build/xmc4700_relax_kit/program.elf",
            "projectEnvName": "xmc4700_relax_kit",
            "toolchainBinDir": "C:/Users/<USER>/.platformio/packages/toolchain-gccarmnoneeabi/bin",
            "internalConsoleOptions": "openOnSessionStart",
            "svdPath": "C:/Users/<USER>/.platformio/platforms/infineonxmc/misc/svd/XMC4700.svd",
            "loadMode": "manual"
        }
    ]
}
