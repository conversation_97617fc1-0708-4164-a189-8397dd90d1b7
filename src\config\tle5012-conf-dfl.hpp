/**
 * @file        tle5012-conf-dfl.hpp
 * @brief       TLE5012 Default Library Configuration
 * @date        Mai 2020
 * @copyright   Copyright (c) 2019-2020 Infineon Technologies AG
 *
 * SPDX-License-Identifier: MIT
 */

#ifndef TLE5012_CONF_DFL_HPP_
#define TLE5012_CONF_DFL_HPP_

/**
 * @addtogroup tle5012frmw
 * @{
 */

#ifndef TLE5012_FRAMEWORK
#error  'TLE5012 error framework undefined'    /**< TLE5012 framework must be defined in conf. or at compilation */
#endif

/** @} */

#endif /** TLE5012_CONF_DFL_HPP_ **/
