#!/usr/bin/env python3
"""
TLE5012B Magnetic Angle Sensor Library for Raspberry Pi 5
Author: AI Assistant
Date: 2024

This library provides an interface to read absolute angular position
from the TLE5012B magnetic angle sensor using SPI communication.

Hardware Connections (Raspberry Pi 5):
- VCC -> 3.3V (Pin 1 or 17)
- GND -> Ground (Pin 6, 9, 14, 20, 25, 30, 34, 39)
- SCK -> GP<PERSON> 11 (Pin 23) - <PERSON><PERSON> Clock
- MISO -> GPIO 9 (Pin 21) - SP<PERSON> MISO
- MOSI -> GPIO 10 (Pin 19) - SPI MOSI  
- CS -> GPIO 8 (Pin 24) - SPI Chip Select (or any GPIO pin)

Enable SPI on Raspberry Pi:
sudo raspi-config -> Interface Options -> SPI -> Enable
"""

import spidev
import time
import struct
from typing import Tuple, Optional
from enum import IntEnum

class TLE5012BError(Exception):
    """Custom exception for TLE5012B sensor errors"""
    pass

class RegisterAddress(IntEnum):
    """TLE5012B Register Addresses"""
    STAT = 0x0000      # Status register
    ACSTAT = 0x0010    # Activation status register  
    AVAL = 0x0020      # Angle value register (main one!)
    ASPD = 0x0030      # Angle speed register
    AREV = 0x0040      # Revolution counter register
    FSYNC = 0x0050     # Frame sync/temperature register
    MOD_1 = 0x0060     # Interface mode 1
    MOD_2 = 0x0080     # Interface mode 2 (angle range)
    ADC_X = 0x0100     # Raw X ADC value
    ADC_Y = 0x0110     # Raw Y ADC value
    T_RAW = 0x0150     # Raw temperature

class TLE5012B:
    """
    TLE5012B Magnetic Angle Sensor Driver for Raspberry Pi
    
    This class provides methods to read absolute angular position,
    temperature, revolution count, and other sensor data.
    """
    
    # Command bit definitions
    READ_COMMAND = 0x8000      # Bit 15: 1 for read
    WRITE_COMMAND = 0x0000     # Bit 15: 0 for write
    OPERATIONAL_ACCESS = 0x0000 # Bits 14:11: operational registers
    CONFIG_ACCESS = 0x5000     # Bits 14:11: configuration registers
    CURRENT_VALUE = 0x0000     # Bit 10: current value
    UPDATE_BUFFER = 0x0400     # Bit 10: update buffer
    SAFETY_WORD = 0x0001       # Enable safety word
    
    # Conversion constants
    ANGLE_RESOLUTION = 360.0 / 32768.0  # 360° / 2^15
    TEMP_RESOLUTION = 1.0 / 4.0         # Temperature LSB = 0.25°C
    SPEED_RESOLUTION = 1.0 / 16.0       # Speed resolution
    
    def __init__(self, spi_bus: int = 0, spi_device: int = 0, cs_pin: Optional[int] = None, 
                 max_speed_hz: int = 1000000):
        """
        Initialize TLE5012B sensor
        
        Args:
            spi_bus: SPI bus number (usually 0)
            spi_device: SPI device number (usually 0)  
            cs_pin: Custom CS pin (None to use hardware CS)
            max_speed_hz: SPI clock speed (max 8MHz for TLE5012B)
        """
        self.spi_bus = spi_bus
        self.spi_device = spi_device
        self.cs_pin = cs_pin
        self.max_speed_hz = min(max_speed_hz, 8000000)  # Sensor max is 8MHz
        
        # Initialize SPI
        self.spi = spidev.SpiDev()
        self.spi.open(spi_bus, spi_device)
        self.spi.max_speed_hz = self.max_speed_hz
        self.spi.mode = 1  # CPOL=0, CPHA=1 (SPI Mode 1)
        self.spi.bits_per_word = 8
        
        # Custom CS pin setup if specified
        if cs_pin is not None:
            try:
                import RPi.GPIO as GPIO
                GPIO.setmode(GPIO.BCM)
                GPIO.setup(cs_pin, GPIO.OUT)
                GPIO.output(cs_pin, GPIO.HIGH)
                self.gpio = GPIO
            except ImportError:
                raise TLE5012BError("RPi.GPIO required for custom CS pin")
        else:
            self.gpio = None
            
        print(f"TLE5012B initialized on SPI {spi_bus}.{spi_device} at {self.max_speed_hz}Hz")
    
    def _create_command(self, register_addr: int, num_words: int = 1, 
                       read: bool = True, safety: bool = True) -> int:
        """
        Create SPI command word for TLE5012B
        
        Args:
            register_addr: Register address (6 bits)
            num_words: Number of 16-bit words to read (1-15)
            read: True for read, False for write
            safety: True to enable safety word
            
        Returns:
            16-bit command word
        """
        command = 0
        
        if read:
            command |= self.READ_COMMAND
            
        # Operational access for addresses 0x00-0x04, config for others
        if register_addr <= 0x0040:
            command |= self.OPERATIONAL_ACCESS
        else:
            command |= self.CONFIG_ACCESS
            
        # Register address (bits 9:4)
        command |= (register_addr >> 4) << 4
        
        # Number of words (bits 3:0)
        command |= (num_words & 0x0F)
        
        # Safety word
        if safety:
            command |= self.SAFETY_WORD
            
        return command
    
    def _spi_transfer(self, command: int, data: Optional[int] = None) -> list:
        """
        Perform SPI transfer with TLE5012B
        
        Args:
            command: 16-bit command word
            data: Optional 16-bit data word for write operations
            
        Returns:
            List of received 16-bit words
        """
        # Prepare transmit data
        tx_data = []
        
        # Add command word (MSB first)
        tx_data.extend([(command >> 8) & 0xFF, command & 0xFF])
        
        # Add data word if writing
        if data is not None:
            tx_data.extend([(data >> 8) & 0xFF, data & 0xFF])
        
        # Calculate expected response length
        num_words = (command & 0x0F) or 1
        safety_word = 1 if (command & self.SAFETY_WORD) else 0
        rx_length = (num_words + safety_word) * 2  # 2 bytes per word
        
        # Pad TX data to match RX length
        while len(tx_data) < rx_length:
            tx_data.append(0x00)
        
        # Custom CS control if specified
        if self.cs_pin is not None:
            self.gpio.output(self.cs_pin, self.gpio.LOW)
            
        try:
            # Perform SPI transfer
            rx_data = self.spi.xfer2(tx_data)
            
            # Convert bytes back to 16-bit words
            words = []
            for i in range(0, len(rx_data), 2):
                if i + 1 < len(rx_data):
                    word = (rx_data[i] << 8) | rx_data[i + 1]
                    words.append(word)
                    
        finally:
            if self.cs_pin is not None:
                self.gpio.output(self.cs_pin, self.gpio.HIGH)
                
        return words
    
    def _check_safety_word(self, safety_word: int) -> None:
        """
        Check safety word for errors
        
        Args:
            safety_word: 16-bit safety word from sensor
            
        Raises:
            TLE5012BError: If safety word indicates an error
        """
        # Extract error flags from safety word
        stat_res = (safety_word >> 15) & 0x01  # System reset
        stat_err = (safety_word >> 14) & 0x01  # System error  
        stat_acc = (safety_word >> 13) & 0x01  # Interface access error
        stat_ang = (safety_word >> 12) & 0x01  # Invalid angle value
        
        if stat_res:
            raise TLE5012BError("Sensor system reset detected")
        if stat_err:
            raise TLE5012BError("Sensor system error")
        if stat_acc:
            raise TLE5012BError("Interface access error")
        if stat_ang:
            raise TLE5012BError("Invalid angle value")
    
    def read_register(self, register_addr: int, safety_check: bool = True) -> int:
        """
        Read a single 16-bit register from the sensor
        
        Args:
            register_addr: Register address to read
            safety_check: Enable safety word checking
            
        Returns:
            16-bit register value
            
        Raises:
            TLE5012BError: If communication or safety check fails
        """
        command = self._create_command(register_addr, 1, True, safety_check)
        response = self._spi_transfer(command)
        
        if len(response) < 1:
            raise TLE5012BError("No response from sensor")
            
        data = response[0]
        
        # Check safety word if enabled
        if safety_check and len(response) > 1:
            self._check_safety_word(response[1])
            
        return data
    
    def get_angle_degrees(self) -> float:
        """
        Read absolute angle position in degrees
        
        Returns:
            Angle in degrees (-180.0 to +180.0)
            
        Raises:
            TLE5012BError: If read fails
        """
        raw_data = self.read_register(RegisterAddress.AVAL)
        
        # Extract 15-bit signed angle value (remove status bit 15)
        angle_raw = raw_data & 0x7FFF
        
        # Convert to signed integer if bit 14 is set (negative)
        if angle_raw & 0x4000:
            angle_raw = angle_raw - 0x8000
            
        # Convert to degrees
        angle_degrees = angle_raw * self.ANGLE_RESOLUTION
        
        return angle_degrees
    
    def get_angle_radians(self) -> float:
        """
        Read absolute angle position in radians
        
        Returns:
            Angle in radians (-π to +π)
        """
        import math
        return math.radians(self.get_angle_degrees())
    
    def get_raw_angle(self) -> int:
        """
        Read raw 15-bit angle value
        
        Returns:
            Raw angle value (-16384 to +16383)
        """
        raw_data = self.read_register(RegisterAddress.AVAL)
        angle_raw = raw_data & 0x7FFF
        
        if angle_raw & 0x4000:
            angle_raw = angle_raw - 0x8000
            
        return angle_raw
    
    def get_revolution_count(self) -> int:
        """
        Read revolution counter (number of full rotations)
        
        Returns:
            Revolution count (-256 to +255)
        """
        raw_data = self.read_register(RegisterAddress.AREV)
        
        # Extract 9-bit signed revolution counter (bits 8:0)
        rev_raw = raw_data & 0x01FF
        
        # Convert to signed if bit 8 is set
        if rev_raw & 0x0100:
            rev_raw = rev_raw - 0x0200
            
        return rev_raw
    
    def get_temperature_celsius(self) -> float:
        """
        Read sensor temperature in Celsius
        
        Returns:
            Temperature in degrees Celsius
        """
        raw_data = self.read_register(RegisterAddress.FSYNC)
        
        # Extract 9-bit signed temperature (bits 8:0)  
        temp_raw = raw_data & 0x01FF
        
        # Convert to signed if bit 8 is set
        if temp_raw & 0x0100:
            temp_raw = temp_raw - 0x0200
            
        # Convert to Celsius (LSB = 0.25°C)
        temperature = temp_raw * self.TEMP_RESOLUTION
        
        return temperature
    
    def get_angle_speed(self) -> float:
        """
        Read angular speed in degrees per second
        
        Returns:
            Angular speed in degrees/second
        """
        raw_data = self.read_register(RegisterAddress.ASPD)
        
        # Extract 15-bit signed speed value
        speed_raw = raw_data & 0x7FFF
        
        if speed_raw & 0x4000:
            speed_raw = speed_raw - 0x8000
            
        # Convert to degrees/second (implementation depends on update rate)
        # This is a simplified conversion - actual formula is more complex
        speed_dps = speed_raw * self.SPEED_RESOLUTION
        
        return speed_dps
    
    def get_status(self) -> dict:
        """
        Read sensor status register
        
        Returns:
            Dictionary with status flags
        """
        status_reg = self.read_register(RegisterAddress.STAT)
        
        return {
            'reset': bool(status_reg & 0x0001),
            'watchdog': bool(status_reg & 0x0002), 
            'voltage_error': bool(status_reg & 0x0004),
            'fuse_error': bool(status_reg & 0x0008),
            'dspu_error': bool(status_reg & 0x0010),
            'overflow': bool(status_reg & 0x0020),
            'xy_out_of_limit': bool(status_reg & 0x0040),
            'magnitude_out_of_limit': bool(status_reg & 0x0080),
            'adc_error': bool(status_reg & 0x0200),
            'rom_error': bool(status_reg & 0x0400),
            'no_gmr_xy': bool(status_reg & 0x0800),
            'no_gmr_angle': bool(status_reg & 0x1000),
            'data_ready': bool(status_reg & 0x8000)
        }
    
    def close(self):
        """Close SPI connection and cleanup GPIO"""
        self.spi.close()
        if self.gpio is not None:
            self.gpio.cleanup()
