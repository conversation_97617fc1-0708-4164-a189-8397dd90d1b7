#!/usr/bin/env python3
"""
Basic TLE5012B Angle Reading Example for Raspberry Pi 5

This example demonstrates how to read absolute angular position
from the TLE5012B magnetic angle sensor.

Hardware Setup:
1. Connect TLE5012B to Raspberry Pi 5 SPI pins:
   - VCC -> 3.3V (Pin 1)
   - GND -> Ground (Pin 6)  
   - SCK -> GPIO 11 (Pin 23)
   - MISO -> GPIO 9 (Pin 21)
   - MOSI -> GPIO 10 (Pin 19)
   - CS -> GPIO 8 (Pin 24)

2. Enable SPI on Raspberry Pi:
   sudo raspi-config -> Interface Options -> SPI -> Enable

3. Install required packages:
   sudo apt update
   sudo apt install python3-spidev python3-rpi.gpio

Usage:
   python3 example_basic_reading.py
"""

import time
import sys
from tle5012b_raspi import TLE5012B, TLE5012BError

def main():
    """Main example function"""
    print("TLE5012B Magnetic Angle Sensor - Basic Reading Example")
    print("=" * 60)
    
    try:
        # Initialize sensor on SPI bus 0, device 0
        # Using hardware CS pin (GPIO 8)
        sensor = TLE5012B(spi_bus=0, spi_device=0, max_speed_hz=1000000)
        
        print("Sensor initialized successfully!")
        print("Reading angle values... (Press Ctrl+C to stop)")
        print()
        
        # Read sensor status first
        status = sensor.get_status()
        print("Sensor Status:")
        for key, value in status.items():
            if value:  # Only show active flags
                print(f"  {key}: {value}")
        print()
        
        # Continuous reading loop
        sample_count = 0
        start_time = time.time()
        
        while True:
            try:
                # Read angle in degrees
                angle_deg = sensor.get_angle_degrees()
                
                # Read angle in radians  
                angle_rad = sensor.get_angle_radians()
                
                # Read raw angle value
                raw_angle = sensor.get_raw_angle()
                
                # Read revolution count
                revolutions = sensor.get_revolution_count()
                
                # Read temperature
                temperature = sensor.get_temperature_celsius()
                
                # Read angular speed
                speed = sensor.get_angle_speed()
                
                # Display results
                print(f"Sample {sample_count:4d}: "
                      f"Angle: {angle_deg:7.2f}° ({angle_rad:6.3f} rad) | "
                      f"Raw: {raw_angle:6d} | "
                      f"Rev: {revolutions:3d} | "
                      f"Temp: {temperature:5.1f}°C | "
                      f"Speed: {speed:6.1f}°/s")
                
                sample_count += 1
                
                # Calculate and display sample rate every 100 samples
                if sample_count % 100 == 0:
                    elapsed = time.time() - start_time
                    rate = sample_count / elapsed
                    print(f"Sample rate: {rate:.1f} Hz")
                
                # Small delay to avoid overwhelming the output
                time.sleep(0.1)
                
            except TLE5012BError as e:
                print(f"Sensor error: {e}")
                time.sleep(1)  # Wait before retrying
                
    except KeyboardInterrupt:
        print("\nStopping...")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
        
    finally:
        # Clean up
        try:
            sensor.close()
            print("Sensor connection closed.")
        except:
            pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
