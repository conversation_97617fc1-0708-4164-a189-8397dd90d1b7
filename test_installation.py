#!/usr/bin/env python3
"""
TLE5012B Installation Test Script for Raspberry Pi 5

This script tests the installation and basic functionality of the TLE5012B
magnetic angle sensor library. Run this script to verify everything is
working correctly before using the main examples.

Usage:
    python3 test_installation.py
"""

import sys
import time

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import spidev
        print("  ✓ spidev imported successfully")
    except ImportError:
        print("  ✗ spidev not found. Install with: sudo apt install python3-spidev")
        return False
    
    try:
        import RPi.GPIO as GPIO
        print("  ✓ RPi.GPIO imported successfully")
    except ImportError:
        print("  ✗ RPi.GPIO not found. Install with: sudo apt install python3-rpi.gpio")
        return False
    
    try:
        from tle5012b_raspi import TLE5012B, TLE5012BError
        print("  ✓ TLE5012B library imported successfully")
    except ImportError as e:
        print(f"  ✗ TLE5012B library not found: {e}")
        print("    Make sure tle5012b_raspi.py is in the same directory")
        return False
    
    return True

def test_spi_device():
    """Test if SPI device is available"""
    print("\nTesting SPI device availability...")
    
    import os
    spi_devices = ['/dev/spidev0.0', '/dev/spidev0.1']
    
    available_devices = []
    for device in spi_devices:
        if os.path.exists(device):
            available_devices.append(device)
            print(f"  ✓ {device} found")
        else:
            print(f"  - {device} not found")
    
    if not available_devices:
        print("  ✗ No SPI devices found!")
        print("    Enable SPI with: sudo raspi-config -> Interface Options -> SPI")
        return False
    
    return True

def test_spi_communication():
    """Test basic SPI communication"""
    print("\nTesting SPI communication...")
    
    try:
        import spidev
        spi = spidev.SpiDev()
        spi.open(0, 0)  # Bus 0, Device 0
        spi.max_speed_hz = 1000000
        spi.mode = 1
        
        # Try a simple transfer
        response = spi.xfer2([0x00, 0x00])
        print(f"  ✓ SPI communication test successful: {response}")
        
        spi.close()
        return True
        
    except Exception as e:
        print(f"  ✗ SPI communication failed: {e}")
        return False

def test_sensor_initialization():
    """Test TLE5012B sensor initialization"""
    print("\nTesting TLE5012B sensor initialization...")
    
    try:
        from tle5012b_raspi import TLE5012B, TLE5012BError
        
        # Try to initialize sensor
        sensor = TLE5012B(spi_bus=0, spi_device=0, max_speed_hz=1000000)
        print("  ✓ Sensor initialized successfully")
        
        # Try to close sensor
        sensor.close()
        print("  ✓ Sensor closed successfully")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Sensor initialization failed: {e}")
        print("    This is normal if no sensor is connected")
        return False

def test_sensor_reading():
    """Test reading data from the sensor"""
    print("\nTesting sensor data reading...")
    
    try:
        from tle5012b_raspi import TLE5012B, TLE5012BError
        
        sensor = TLE5012B(spi_bus=0, spi_device=0, max_speed_hz=1000000)
        
        # Try to read angle
        angle = sensor.get_angle_degrees()
        print(f"  ✓ Angle reading successful: {angle:.2f}°")
        
        # Try to read temperature
        temp = sensor.get_temperature_celsius()
        print(f"  ✓ Temperature reading successful: {temp:.1f}°C")
        
        # Try to read status
        status = sensor.get_status()
        active_flags = [k for k, v in status.items() if v]
        print(f"  ✓ Status reading successful. Active flags: {active_flags}")
        
        sensor.close()
        return True
        
    except TLE5012BError as e:
        print(f"  ⚠ Sensor error (expected if no sensor connected): {e}")
        return False
    except Exception as e:
        print(f"  ✗ Unexpected error: {e}")
        return False

def test_optional_features():
    """Test optional features"""
    print("\nTesting optional features...")
    
    # Test matplotlib (for plotting)
    try:
        import matplotlib.pyplot as plt
        print("  ✓ matplotlib available (plotting enabled)")
    except ImportError:
        print("  - matplotlib not available (install with: pip3 install matplotlib)")
    
    # Test numpy (for advanced calculations)
    try:
        import numpy as np
        print("  ✓ numpy available")
    except ImportError:
        print("  - numpy not available (install with: pip3 install numpy)")
    
    # Test csv (should be built-in)
    try:
        import csv
        print("  ✓ csv module available (data logging enabled)")
    except ImportError:
        print("  - csv module not available")

def main():
    """Main test function"""
    print("TLE5012B Installation Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Imports
    total_tests += 1
    if test_imports():
        tests_passed += 1
    
    # Test 2: SPI device availability
    total_tests += 1
    if test_spi_device():
        tests_passed += 1
    
    # Test 3: SPI communication
    total_tests += 1
    if test_spi_communication():
        tests_passed += 1
    
    # Test 4: Sensor initialization
    total_tests += 1
    if test_sensor_initialization():
        tests_passed += 1
    
    # Test 5: Sensor reading (only if sensor is connected)
    print("\n" + "="*40)
    print("SENSOR HARDWARE TESTS")
    print("(These tests require a connected TLE5012B sensor)")
    print("="*40)
    
    total_tests += 1
    if test_sensor_reading():
        tests_passed += 1
    
    # Test optional features
    test_optional_features()
    
    # Summary
    print("\n" + "="*40)
    print("TEST SUMMARY")
    print("="*40)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed >= 3:  # First 3 tests are essential
        print("✓ Basic installation is working!")
        if tests_passed == total_tests:
            print("✓ All tests passed - sensor is working perfectly!")
        else:
            print("⚠ Some sensor tests failed - check hardware connections")
    else:
        print("✗ Installation issues detected - check the failed tests above")
        return 1
    
    print("\nNext steps:")
    print("1. Connect your TLE5012B sensor to the Raspberry Pi")
    print("2. Run: python3 example_basic_reading.py")
    print("3. For advanced features: python3 example_advanced_features.py --help")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
