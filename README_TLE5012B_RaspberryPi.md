# TLE5012B Magnetic Angle Sensor for Raspberry Pi 5

This project provides a Python library and examples for using the TLE5012B magnetic angle sensor with Raspberry Pi 5. The TLE5012B is a high-precision 360° magnetic angle sensor with 15-bit resolution (0.01° precision).

## Features

- **High Precision**: 15-bit resolution (0.01° precision)
- **360° Measurement**: Full rotation sensing with revolution counting
- **Multiple Interfaces**: SSC (SPI), PWM, IIF, HSM, SPC
- **Temperature Sensing**: Built-in temperature measurement
- **Speed Calculation**: Angular velocity measurement
- **Safety Features**: Built-in error detection and CRC checking

## Hardware Requirements

### TLE5012B Sensor Variants
- **TLE5012B-E1000**: Sensor2Go kit (recommended for beginners)
- **TLE5012B-E5000**: Breakout board
- **TLE5012B-E9000**: SPC interface variant
- **Bulk chip**: Requires custom PCB

### Raspberry Pi 5 Connections

| TLE5012B Pin | Raspberry Pi 5 Pin | GPIO | Function |
|--------------|-------------------|------|----------|
| VCC          | Pin 1 or 17       | 3.3V | Power Supply |
| GND          | Pin 6, 9, 14, etc| GND  | Ground |
| SCK          | Pin 23            | GPIO 11 | SPI Clock |
| MISO         | Pin 21            | GPIO 9  | SPI MISO |
| MOSI         | Pin 19            | GPIO 10 | SPI MOSI |
| CS/CSQ       | Pin 24            | GPIO 8  | Chip Select |

### Optional Connections
- **IFA/IFB/IFC**: Additional interface pins (for PWM, IIF modes)
- **EN**: Enable pin (Sensor2Go only)

## Software Installation

### 1. Enable SPI on Raspberry Pi
```bash
sudo raspi-config
# Navigate to: Interface Options -> SPI -> Enable
sudo reboot
```

### 2. Install Required Python Packages
```bash
# Update system
sudo apt update
sudo apt upgrade

# Install Python SPI library
sudo apt install python3-spidev python3-rpi.gpio

# Optional: Install matplotlib for plotting
pip3 install matplotlib

# Optional: Install additional scientific libraries
pip3 install numpy scipy pandas
```

### 3. Download the Library Files
```bash
# Create project directory
mkdir ~/tle5012b_project
cd ~/tle5012b_project

# Copy the library files:
# - tle5012b_raspi.py
# - example_basic_reading.py  
# - example_advanced_features.py
```

### 4. Test SPI Connection
```bash
# Check if SPI is enabled
ls /dev/spi*
# Should show: /dev/spidev0.0  /dev/spidev0.1

# Test basic SPI communication
python3 -c "import spidev; print('SPI available')"
```

## Usage Examples

### Basic Angle Reading
```python
from tle5012b_raspi import TLE5012B

# Initialize sensor
sensor = TLE5012B(spi_bus=0, spi_device=0)

# Read angle in degrees
angle = sensor.get_angle_degrees()
print(f"Angle: {angle:.2f}°")

# Read angle in radians
angle_rad = sensor.get_angle_radians()
print(f"Angle: {angle_rad:.3f} rad")

# Read revolution count
revolutions = sensor.get_revolution_count()
print(f"Revolutions: {revolutions}")

# Read temperature
temp = sensor.get_temperature_celsius()
print(f"Temperature: {temp:.1f}°C")

# Clean up
sensor.close()
```

### Running the Examples

#### Basic Reading Example
```bash
python3 example_basic_reading.py
```

#### Advanced Features Example
```bash
# Basic usage
python3 example_advanced_features.py

# With real-time plotting
python3 example_advanced_features.py --plot

# With data logging
python3 example_advanced_features.py --log sensor_data.csv

# With custom CS pin
python3 example_advanced_features.py --cs-pin 18

# With custom SPI speed
python3 example_advanced_features.py --speed 2000000
```

## Understanding the TLE5012B Registers

### Key Registers
- **STAT (0x0000)**: Status register with error flags
- **AVAL (0x0020)**: Angle value register (main data)
- **ASPD (0x0030)**: Angular speed register
- **AREV (0x0040)**: Revolution counter
- **FSYNC (0x0050)**: Temperature data

### Register Structure
Each register is 16 bits (2 bytes):
- **AVAL Register**: 
  - Bits 14:0 = Signed angle value (-16384 to +16383)
  - Bit 15 = Data ready flag
  - Conversion: angle_degrees = (raw_value / 32768) * 360

### Communication Protocol
The sensor uses SPI with a specific command structure:
- **Command Word (16-bit)**:
  - Bit 15: 0=Write, 1=Read
  - Bits 14:11: Access type (0000=operational, 1010=config)
  - Bit 10: 0=current value, 1=update buffer
  - Bits 9:4: Register address (6 bits)
  - Bits 3:0: Number of words to read

## Troubleshooting

### Common Issues

#### 1. "No response from sensor"
- Check wiring connections
- Verify 3.3V power supply
- Ensure SPI is enabled: `ls /dev/spi*`
- Try lower SPI speed: `max_speed_hz=500000`

#### 2. "Interface access error"
- Check CS pin connection
- Verify SPI mode (should be mode 1)
- Try adding delays between readings

#### 3. "Invalid angle value"
- Check magnet placement (should be centered over sensor)
- Verify magnet strength (recommended: 10-50 mT)
- Ensure magnet is perpendicular to sensor surface

#### 4. Inconsistent readings
- Check for electromagnetic interference
- Verify stable power supply
- Try enabling safety word checking
- Check temperature - sensor may need calibration

### Diagnostic Commands
```python
# Check sensor status
status = sensor.get_status()
print("Status flags:", {k: v for k, v in status.items() if v})

# Read raw register values
raw_angle = sensor.read_register(0x0020)  # AVAL register
print(f"Raw AVAL: 0x{raw_angle:04X}")

# Test SPI communication
try:
    sensor = TLE5012B()
    print("Sensor initialized successfully")
except Exception as e:
    print(f"Initialization failed: {e}")
```

## Advanced Configuration

### Custom CS Pin
```python
# Use GPIO 18 as CS pin instead of hardware CS
sensor = TLE5012B(cs_pin=18)
```

### Multiple Sensors
```python
# Sensor 1 on hardware CS
sensor1 = TLE5012B(spi_bus=0, spi_device=0)

# Sensor 2 on custom CS pin
sensor2 = TLE5012B(spi_bus=0, spi_device=0, cs_pin=18)
```

### High-Speed Reading
```python
# Increase SPI speed for faster readings
sensor = TLE5012B(max_speed_hz=4000000)  # 4 MHz

# Disable safety checking for speed
angle = sensor.read_register(0x0020, safety_check=False)
```

## Performance Specifications

- **Resolution**: 15 bits (0.01°)
- **Accuracy**: ±1.0° (with auto-calibration)
- **Update Rate**: Up to 1 kHz
- **SPI Speed**: Up to 8 MHz
- **Temperature Range**: -40°C to +150°C
- **Supply Voltage**: 3.3V ±10%

## References

- [TLE5012B Datasheet](https://www.infineon.com/cms/en/product/sensor/magnetic-sensors/magnetic-position-sensors/angle-sensors/tle5012b/)
- [TLE5012B User Manual](https://www.infineon.com/dgdl/Infineon-Angle_Sensor_TLE5012B-UM-v01_02-en-UM-v01_02-EN.pdf)
- [Raspberry Pi SPI Documentation](https://www.raspberrypi.org/documentation/hardware/raspberrypi/spi/)

## License

This project is provided under the MIT License. See LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.
