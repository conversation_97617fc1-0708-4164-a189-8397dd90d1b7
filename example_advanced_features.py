#!/usr/bin/env python3
"""
Advanced TLE5012B Features Example for Raspberry Pi 5

This example demonstrates advanced features like:
- Multi-turn absolute positioning (with revolution counting)
- Data logging to CSV file
- Real-time plotting (optional)
- Error handling and diagnostics
- Custom CS pin usage

Usage:
   python3 example_advanced_features.py [--plot] [--log filename.csv]
"""

import time
import sys
import argparse
import csv
from datetime import datetime
from tle5012b_raspi import TLE5012B, TLE5012BError

def log_data_to_csv(filename, data):
    """Log sensor data to CSV file"""
    file_exists = False
    try:
        with open(filename, 'r'):
            file_exists = True
    except FileNotFoundError:
        pass
    
    with open(filename, 'a', newline='') as csvfile:
        fieldnames = ['timestamp', 'angle_deg', 'angle_rad', 'raw_angle', 
                     'revolutions', 'absolute_angle', 'temperature', 'speed']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        if not file_exists:
            writer.writeheader()
        
        writer.writerow(data)

def setup_plotting():
    """Setup real-time plotting (optional)"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.animation as animation
        from collections import deque
        
        # Setup plot
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 8))
        fig.suptitle('TLE5012B Real-time Data')
        
        # Data storage
        max_points = 200
        times = deque(maxlen=max_points)
        angles = deque(maxlen=max_points)
        temps = deque(maxlen=max_points)
        speeds = deque(maxlen=max_points)
        
        # Plot setup
        line1, = ax1.plot([], [], 'b-', label='Angle (°)')
        line2, = ax2.plot([], [], 'r-', label='Temperature (°C)')
        line3, = ax3.plot([], [], 'g-', label='Speed (°/s)')
        
        ax1.set_ylabel('Angle (°)')
        ax1.set_ylim(-180, 180)
        ax1.grid(True)
        ax1.legend()
        
        ax2.set_ylabel('Temperature (°C)')
        ax2.grid(True)
        ax2.legend()
        
        ax3.set_ylabel('Speed (°/s)')
        ax3.set_xlabel('Time (s)')
        ax3.grid(True)
        ax3.legend()
        
        return fig, (ax1, ax2, ax3), (line1, line2, line3), (times, angles, temps, speeds)
        
    except ImportError:
        print("Matplotlib not available. Install with: pip3 install matplotlib")
        return None, None, None, None

def update_plot(sensor, plot_data, start_time):
    """Update real-time plot"""
    if plot_data[0] is None:  # No plotting setup
        return
        
    fig, axes, lines, data_queues = plot_data
    times, angles, temps, speeds = data_queues
    line1, line2, line3 = lines
    ax1, ax2, ax3 = axes
    
    try:
        # Read sensor data
        angle = sensor.get_angle_degrees()
        temp = sensor.get_temperature_celsius()
        speed = sensor.get_angle_speed()
        current_time = time.time() - start_time
        
        # Update data queues
        times.append(current_time)
        angles.append(angle)
        temps.append(temp)
        speeds.append(speed)
        
        # Update plots
        line1.set_data(times, angles)
        line2.set_data(times, temps)
        line3.set_data(times, speeds)
        
        # Update axes limits
        if len(times) > 1:
            time_range = max(times) - min(times)
            for ax in axes:
                ax.set_xlim(min(times), max(times))
            
            # Auto-scale temperature and speed axes
            if len(temps) > 1:
                temp_range = max(temps) - min(temps)
                ax2.set_ylim(min(temps) - temp_range*0.1, max(temps) + temp_range*0.1)
            
            if len(speeds) > 1 and max(speeds) != min(speeds):
                speed_range = max(speeds) - min(speeds)
                ax3.set_ylim(min(speeds) - speed_range*0.1, max(speeds) + speed_range*0.1)
        
        fig.canvas.draw()
        fig.canvas.flush_events()
        
    except Exception as e:
        print(f"Plot update error: {e}")

def main():
    """Main example function"""
    parser = argparse.ArgumentParser(description='TLE5012B Advanced Features Example')
    parser.add_argument('--plot', action='store_true', help='Enable real-time plotting')
    parser.add_argument('--log', type=str, help='Log data to CSV file')
    parser.add_argument('--cs-pin', type=int, help='Custom CS pin number (BCM)')
    parser.add_argument('--speed', type=int, default=1000000, help='SPI speed in Hz')
    args = parser.parse_args()
    
    print("TLE5012B Magnetic Angle Sensor - Advanced Features Example")
    print("=" * 65)
    
    # Setup plotting if requested
    plot_data = (None, None, None, None)
    if args.plot:
        plot_data = setup_plotting()
        if plot_data[0] is not None:
            print("Real-time plotting enabled")
            import matplotlib.pyplot as plt
            plt.ion()  # Interactive mode
    
    try:
        # Initialize sensor
        if args.cs_pin:
            print(f"Using custom CS pin: GPIO {args.cs_pin}")
            sensor = TLE5012B(spi_bus=0, spi_device=0, cs_pin=args.cs_pin, 
                            max_speed_hz=args.speed)
        else:
            print("Using hardware CS pin (GPIO 8)")
            sensor = TLE5012B(spi_bus=0, spi_device=0, max_speed_hz=args.speed)
        
        print("Sensor initialized successfully!")
        
        # Check sensor status
        status = sensor.get_status()
        print("\nSensor Status Check:")
        error_flags = [key for key, value in status.items() if value and 'error' in key]
        if error_flags:
            print(f"  WARNING: Error flags detected: {error_flags}")
        else:
            print("  All status flags OK")
        
        if not status['data_ready']:
            print("  WARNING: Data not ready flag")
        
        print(f"\nStarting continuous reading... (Press Ctrl+C to stop)")
        if args.log:
            print(f"Logging data to: {args.log}")
        print()
        
        # Initialize tracking variables
        sample_count = 0
        start_time = time.time()
        last_angle = None
        total_rotation = 0.0  # Track total rotation including multiple turns
        
        while True:
            try:
                # Read all sensor values
                angle_deg = sensor.get_angle_degrees()
                angle_rad = sensor.get_angle_radians()
                raw_angle = sensor.get_raw_angle()
                revolutions = sensor.get_revolution_count()
                temperature = sensor.get_temperature_celsius()
                speed = sensor.get_angle_speed()
                
                # Calculate multi-turn absolute position
                # This accounts for continuous rotation beyond ±180°
                if last_angle is not None:
                    angle_diff = angle_deg - last_angle
                    # Handle wraparound at ±180°
                    if angle_diff > 180:
                        angle_diff -= 360
                    elif angle_diff < -180:
                        angle_diff += 360
                    total_rotation += angle_diff
                else:
                    total_rotation = angle_deg
                
                last_angle = angle_deg
                
                # Alternative absolute angle using revolution counter
                absolute_angle_rev = revolutions * 360.0 + angle_deg
                
                # Display results
                print(f"Sample {sample_count:4d}: "
                      f"Angle: {angle_deg:7.2f}° | "
                      f"Multi-turn: {total_rotation:8.1f}° | "
                      f"Rev-based: {absolute_angle_rev:8.1f}° | "
                      f"Rev: {revolutions:3d} | "
                      f"Temp: {temperature:5.1f}°C | "
                      f"Speed: {speed:6.1f}°/s")
                
                # Log data if requested
                if args.log:
                    log_data = {
                        'timestamp': datetime.now().isoformat(),
                        'angle_deg': angle_deg,
                        'angle_rad': angle_rad,
                        'raw_angle': raw_angle,
                        'revolutions': revolutions,
                        'absolute_angle': total_rotation,
                        'temperature': temperature,
                        'speed': speed
                    }
                    log_data_to_csv(args.log, log_data)
                
                # Update plot if enabled
                if args.plot:
                    update_plot(sensor, plot_data, start_time)
                
                sample_count += 1
                
                # Display sample rate every 50 samples
                if sample_count % 50 == 0:
                    elapsed = time.time() - start_time
                    rate = sample_count / elapsed
                    print(f"  -> Sample rate: {rate:.1f} Hz, Runtime: {elapsed:.1f}s")
                
                # Adjust delay based on plotting
                delay = 0.05 if args.plot else 0.1
                time.sleep(delay)
                
            except TLE5012BError as e:
                print(f"Sensor error: {e}")
                # Try to read status for diagnostics
                try:
                    status = sensor.get_status()
                    active_errors = [k for k, v in status.items() if v and 'error' in k]
                    if active_errors:
                        print(f"  Active error flags: {active_errors}")
                except:
                    print("  Could not read sensor status")
                time.sleep(1)
                
    except KeyboardInterrupt:
        print("\nStopping...")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
        
    finally:
        # Clean up
        try:
            sensor.close()
            print("Sensor connection closed.")
        except:
            pass
        
        if args.plot and plot_data[0] is not None:
            try:
                import matplotlib.pyplot as plt
                plt.ioff()
                plt.show()  # Keep plot window open
            except:
                pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
