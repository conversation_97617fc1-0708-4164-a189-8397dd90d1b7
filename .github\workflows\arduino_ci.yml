name: Arduino CI

on: [push, pull_request]

jobs:
  build:

    strategy:
      matrix:
        example: [
            examples/E9000SPC,
            examples/readAngleSpeedRevolutions,
            examples/readAngleTest,
            examples/readAngleValueProcessing,
            examples/readMultipleRegisters,
            examples/readSpeedProcessing,
            examples/sensorRegisters,
            examples/sensorType,
            examples/testSensorMainValues,
            examples/useMultipleSensors,
            examples/writeRegisters
        ]

        arduino-platform: ["arduino:avr"]

        include:
          - arduino-platform: "arduino:avr"
            fqbn: "arduino:avr:uno"

    runs-on: windows-latest

    steps:
      - name: Checkout
        uses: actions/checkout@master

      - name: Setup Arduino CLI
        uses: arduino/setup-arduino-cli@v1.1.1

      - name: Install platform
        run: |
          arduino-cli core update-index
          arduino-cli core install ${{ matrix.arduino-platform }}

      - name: Install repo as library
        run: |
          mkdir -p "$HOME/Arduino/libraries"
          ln -s "$PWD" "$HOME/Arduino/libraries/."

      - name: Compile Sketch
        run: arduino-cli compile --fqbn ${{ matrix.fqbn }} --libraries="." --libraries="$HOME/Arduino/libraries/." ${{ matrix.example }}

